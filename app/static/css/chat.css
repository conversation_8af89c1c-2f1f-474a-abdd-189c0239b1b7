/* ===== MODERN CHAT INTERFACE STYLES ===== */

/* Body and Layout Overrides for Chat Page */
body {
    margin: 0;
    padding: 0;
    font-family: 'Open Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--bg-primary);
    overflow: hidden;
    height: 100vh;
}

/* ===== FULL-SCREEN APP LAYOUT ===== */
.app-layout {
    display: flex;
    height: 100vh;
    width: 100vw;
    overflow: hidden;
}

/* ===== SIDEBAR ===== */
.sidebar {
    width: 280px;
    background: var(--bg-secondary);
    border-right: 1px solid var(--ai-message-border);
    display: flex;
    flex-direction: column;
    transition: transform var(--transition-normal);
    z-index: 200;
    position: relative;
}

.sidebar-header {
    display: flex;
    align-items: center;
    padding: var(--space-md) var(--space-lg);
    border-bottom: 1px solid var(--ai-message-border);
    gap: var(--space-md);
}

.sidebar-toggle {
    display: none;
    width: 32px;
    height: 32px;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.sidebar-toggle:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.sidebar-logo {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    font-weight: 600;
    color: var(--text-primary);
    font-size: 1.1rem;
}

.sidebar-logo i {
    color: var(--primary-color);
    font-size: 1.25rem;
}

.sidebar-content {
    flex: 1;
    overflow-y: auto;
    padding: var(--space-lg);
    display: flex;
    flex-direction: column;
    gap: var(--space-xl);
}

/* New Chat Button */
.new-chat-btn {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    width: 100%;
    padding: var(--space-md) var(--space-lg);
    background: var(--primary-gradient);
    color: var(--text-inverse);
    border: none;
    border-radius: var(--radius-lg);
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    box-shadow: var(--shadow-sm);
}

.new-chat-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.new-chat-btn i {
    font-size: 0.9rem;
}

/* Navigation */
.sidebar-nav {
    display: flex;
    flex-direction: column;
    gap: var(--space-lg);
}

.nav-section {
    display: flex;
    flex-direction: column;
    gap: var(--space-sm);
}

.nav-section-title {
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin: 0;
    padding: 0 var(--space-sm);
}

.nav-list {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    flex-direction: column;
    gap: var(--space-xs);
}

.nav-item {
    margin: 0;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    width: 100%;
    padding: var(--space-sm) var(--space-md);
    color: var(--text-secondary);
    text-decoration: none;
    border: none;
    background: transparent;
    border-radius: var(--radius-md);
    font-size: 0.9rem;
    cursor: pointer;
    transition: all var(--transition-fast);
    text-align: left;
}

.nav-link:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.nav-link.active {
    background: var(--primary-color);
    color: var(--text-inverse);
}

.nav-link i {
    width: 16px;
    text-align: center;
    font-size: 0.9rem;
}

/* Chat History */
.chat-history {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--space-sm);
    min-height: 0;
}

.history-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-xs);
    overflow-y: auto;
    max-height: 300px;
}

.history-item {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.history-item:hover {
    background: var(--bg-tertiary);
}

.history-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 2px;
    min-width: 0;
}

.history-title {
    font-size: 0.85rem;
    color: var(--text-primary);
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.history-date {
    font-size: 0.75rem;
    color: var(--text-muted);
}

.history-action {
    width: 24px;
    height: 24px;
    border: none;
    background: transparent;
    color: var(--text-muted);
    cursor: pointer;
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all var(--transition-fast);
    font-size: 0.75rem;
}

.history-item:hover .history-action {
    opacity: 1;
}

.history-action:hover {
    background: var(--bg-primary);
    color: #ef4444;
}

/* Sidebar Footer */
.sidebar-footer {
    padding: var(--space-lg);
    border-top: 1px solid var(--ai-message-border);
}

.user-profile {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-sm);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.user-profile:hover {
    background: var(--bg-tertiary);
}

.user-avatar {
    width: 36px;
    height: 36px;
    background: var(--primary-gradient);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-inverse);
    font-size: 0.9rem;
    flex-shrink: 0;
}

.user-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 2px;
    min-width: 0;
}

.user-name {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-primary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-status {
    font-size: 0.75rem;
    color: var(--text-muted);
}

.user-menu-btn {
    width: 24px;
    height: 24px;
    border: none;
    background: transparent;
    color: var(--text-muted);
    cursor: pointer;
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
    font-size: 0.75rem;
}

.user-menu-btn:hover {
    background: var(--bg-primary);
    color: var(--text-primary);
}

/* ===== CHAT INTERFACE ===== */
.chat-interface {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow: hidden;
    background: var(--bg-primary);
    position: relative;
}

/* Mobile Header */
.mobile-header {
    display: none;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-md);
    background: var(--bg-primary);
    border-bottom: 1px solid var(--ai-message-border);
    position: sticky;
    top: 0;
    z-index: 100;
}

.mobile-sidebar-toggle {
    width: 40px;
    height: 40px;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    border-radius: var(--radius-lg);
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
}

.mobile-sidebar-toggle:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.mobile-title {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 1.1rem;
}

.action-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    border-radius: var(--radius-lg);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
    font-size: 0.9rem;
}

.action-btn:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

/* Sidebar Overlay for Mobile */
.sidebar-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 150;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.sidebar-overlay.active {
    display: block;
    opacity: 1;
}

/* CSS Custom Properties for Theme Support */
:root {
    /* Primary Colors */
    --primary-gradient: linear-gradient(135deg, #3346FF, #5E76FF);
    --primary-color: #3346FF;
    --primary-hover: #2835cc;

    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-overlay: rgba(255, 255, 255, 0.95);

    /* Text Colors */
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    --text-inverse: #ffffff;

    /* Message Colors */
    --user-message-bg: var(--primary-gradient);
    --ai-message-bg: #f8fafc;
    --ai-message-border: #e2e8f0;

    /* Interactive Elements */
    --input-bg: #ffffff;
    --input-border: #e2e8f0;
    --input-focus: var(--primary-color);
    --button-disabled: #cbd5e1;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Spacing */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-2xl: 3rem;

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --radius-full: 9999px;

    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;
}

/* Dark Theme Support */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-primary: #0f172a;
        --bg-secondary: #1e293b;
        --bg-tertiary: #334155;
        --bg-overlay: rgba(15, 23, 42, 0.95);

        --text-primary: #f8fafc;
        --text-secondary: #cbd5e1;
        --text-muted: #94a3b8;

        --ai-message-bg: #1e293b;
        --ai-message-border: #334155;

        --input-bg: #1e293b;
        --input-border: #334155;
    }
}



/* ===== CHAT MESSAGES AREA ===== */
.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: var(--space-lg) var(--space-xl);
    display: flex;
    flex-direction: column;
    gap: var(--space-lg);
    scroll-behavior: smooth;
}

/* Custom Scrollbar */
.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: var(--bg-tertiary);
    border-radius: var(--radius-full);
}

.chat-messages::-webkit-scrollbar-thumb {
    background: var(--text-muted);
    border-radius: var(--radius-full);
    transition: background var(--transition-fast);
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
}

/* ===== WELCOME MESSAGE ===== */
.welcome-message {
    display: flex;
    align-items: flex-start;
    gap: var(--space-md);
    padding: var(--space-lg);
    background: linear-gradient(135deg, var(--bg-tertiary), var(--bg-secondary));
    border-radius: var(--radius-xl);
    border: 1px solid var(--ai-message-border);
    animation: slideInUp var(--transition-slow);
}

.welcome-message .ai-avatar {
    width: 40px;
    height: 40px;
    font-size: 1.125rem;
    flex-shrink: 0;
}

.message-content {
    flex: 1;
}

.message-content p {
    margin: 0 0 var(--space-md) 0;
    color: var(--text-primary);
    line-height: 1.6;
    font-size: 0.95rem;
}

.message-content p:last-child {
    margin-bottom: 0;
}

.message-content ul {
    margin: var(--space-md) 0;
    padding-left: var(--space-lg);
    color: var(--text-secondary);
}

.message-content li {
    margin-bottom: var(--space-xs);
    line-height: 1.5;
    font-size: 0.9rem;
}

/* ===== CHAT MESSAGES ===== */
.message {
    display: flex;
    align-items: flex-end;
    gap: var(--space-sm);
    margin-bottom: var(--space-lg);
    animation: messageSlideIn var(--transition-normal) ease-out;
}

.user-message {
    flex-direction: row-reverse;
    align-self: flex-end;
}

.ai-message {
    align-self: flex-start;
}

/* Message Avatars */
.user-avatar,
.ai-avatar {
    width: 32px;
    height: 32px;
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    flex-shrink: 0;
    transition: transform var(--transition-fast);
}

.user-avatar {
    background: linear-gradient(135deg, #10b981, #059669);
    color: var(--text-inverse);
    box-shadow: var(--shadow-sm);
}

.ai-avatar {
    background: var(--primary-gradient);
    color: var(--text-inverse);
    box-shadow: var(--shadow-sm);
}

.message:hover .user-avatar,
.message:hover .ai-avatar {
    transform: scale(1.1);
}

/* Message Bubbles */
.message-bubble {
    max-width: 70%;
    padding: var(--space-md) var(--space-lg);
    border-radius: var(--radius-xl);
    font-size: 0.95rem;
    line-height: 1.5;
    word-wrap: break-word;
    position: relative;
    transition: all var(--transition-fast);
}

.user-message .message-bubble {
    background: var(--user-message-bg);
    color: var(--text-inverse);
    border-bottom-right-radius: var(--radius-sm);
    box-shadow: var(--shadow-md);
}

.ai-message .message-bubble {
    background: var(--ai-message-bg);
    color: var(--text-primary);
    border: 1px solid var(--ai-message-border);
    border-bottom-left-radius: var(--radius-sm);
    box-shadow: var(--shadow-sm);
}

.message-bubble:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

/* Message Bubble Content */
.message-bubble p {
    margin: 0 0 var(--space-sm) 0;
}

.message-bubble p:last-child {
    margin-bottom: 0;
}

.message-bubble ul,
.message-bubble ol {
    margin: var(--space-sm) 0;
    padding-left: var(--space-lg);
}

.message-bubble li {
    margin-bottom: var(--space-xs);
}

.message-bubble code {
    background: rgba(0, 0, 0, 0.1);
    padding: 2px 6px;
    border-radius: var(--radius-sm);
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.85em;
}

.user-message .message-bubble code {
    background: rgba(255, 255, 255, 0.2);
}

/* ===== TYPING INDICATOR ===== */
.typing-indicator {
    display: flex;
    align-items: flex-end;
    gap: var(--space-sm);
    margin-bottom: var(--space-lg);
    animation: fadeIn var(--transition-normal);
}

.typing-indicator .ai-avatar {
    width: 32px;
    height: 32px;
    font-size: 0.875rem;
}

.typing-bubble {
    background: var(--ai-message-bg);
    border: 1px solid var(--ai-message-border);
    border-radius: var(--radius-xl);
    border-bottom-left-radius: var(--radius-sm);
    padding: var(--space-md) var(--space-lg);
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    box-shadow: var(--shadow-sm);
}

.typing-text {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-style: italic;
}

.typing-dots {
    display: flex;
    gap: 4px;
    align-items: center;
}

.typing-dot {
    width: 6px;
    height: 6px;
    background: var(--text-muted);
    border-radius: var(--radius-full);
    animation: typingDot 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) {
    animation-delay: 0s;
}

.typing-dot:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
    animation-delay: 0.4s;
}

/* ===== INPUT AREA ===== */
.input-area {
    padding: var(--space-lg) var(--space-xl);
    background: var(--bg-overlay);
    backdrop-filter: blur(20px);
    border-top: 1px solid var(--ai-message-border);
    position: sticky;
    bottom: 0;
    z-index: 50;
}

.input-container {
    max-width: 100%;
    margin: 0 auto;
}

.input-wrapper {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    background: var(--input-bg);
    border: 2px solid var(--input-border);
    border-radius: var(--radius-2xl);
    padding: var(--space-sm);
    transition: all var(--transition-fast);
    box-shadow: var(--shadow-sm);
}

.input-wrapper:focus-within {
    border-color: var(--input-focus);
    box-shadow: 0 0 0 3px rgba(51, 70, 255, 0.1);
    transform: translateY(-1px);
}

#user-input {
    flex: 1;
    border: none;
    outline: none;
    background: transparent;
    font-size: 1rem;
    color: var(--text-primary);
    padding: var(--space-md) var(--space-lg);
    line-height: 1.5;
    resize: none;
    font-family: inherit;
}

#user-input::placeholder {
    color: var(--text-muted);
    font-style: italic;
}

#user-input:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

#send-btn {
    width: 44px;
    height: 44px;
    border: none;
    background: var(--primary-gradient);
    color: var(--text-inverse);
    border-radius: var(--radius-full);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    transition: all var(--transition-fast);
    box-shadow: var(--shadow-md);
    flex-shrink: 0;
}

#send-btn:hover:not(:disabled) {
    transform: translateY(-2px) scale(1.05);
    box-shadow: var(--shadow-lg);
}

#send-btn:active:not(:disabled) {
    transform: translateY(0) scale(0.95);
}

#send-btn:disabled {
    background: var(--button-disabled);
    cursor: not-allowed;
    transform: none;
    box-shadow: var(--shadow-sm);
}

.input-footer {
    display: flex;
    justify-content: center;
    margin-top: var(--space-md);
}

.powered-by {
    font-size: 0.8rem;
    color: var(--text-muted);
    display: flex;
    align-items: center;
    gap: var(--space-xs);
}

.powered-by i {
    font-size: 0.75rem;
    color: var(--primary-color);
}

/* ===== ANIMATIONS ===== */
@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes typingDot {
    0%, 60%, 100% {
        transform: translateY(0);
        opacity: 0.4;
    }
    30% {
        transform: translateY(-10px);
        opacity: 1;
    }
}

@keyframes pulse-dot {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.5;
        transform: scale(0.8);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

/* Loading state for messages */
.message-loading {
    background: linear-gradient(
        90deg,
        var(--bg-tertiary) 0%,
        var(--bg-secondary) 50%,
        var(--bg-tertiary) 100%
    );
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

/* ===== RESPONSIVE DESIGN ===== */

/* Large Desktop */
@media (min-width: 1200px) {
    .chat-container {
        max-width: 1400px;
    }

    .chat-messages {
        padding: var(--space-xl) var(--space-2xl);
    }

    .input-area {
        padding: var(--space-xl) var(--space-2xl);
    }

    .message-bubble {
        max-width: 60%;
        font-size: 1rem;
    }
}

/* Desktop */
@media (max-width: 1199px) and (min-width: 992px) {
    .chat-container {
        max-width: 1000px;
    }

    .message-bubble {
        max-width: 65%;
    }
}

/* Tablet */
@media (max-width: 991px) and (min-width: 768px) {
    .sidebar {
        width: 240px;
    }

    .sidebar-content {
        padding: var(--space-md);
        gap: var(--space-lg);
    }

    .top-header {
        padding: var(--space-md) var(--space-lg);
    }

    .chat-messages {
        padding: var(--space-md) var(--space-lg);
        gap: var(--space-md);
    }

    .input-area {
        padding: var(--space-md) var(--space-lg);
    }

    .message-bubble {
        max-width: 75%;
        font-size: 0.9rem;
    }

    .welcome-message {
        padding: var(--space-md);
    }

    .ai-avatar {
        width: 44px;
        height: 44px;
        font-size: 1.1rem;
    }

    .user-avatar,
    .ai-avatar {
        width: 30px;
        height: 30px;
        font-size: 0.8rem;
    }
}

/* Mobile */
@media (max-width: 767px) {
    /* Sidebar becomes overlay on mobile */
    .sidebar {
        position: fixed;
        top: 0;
        left: 0;
        height: 100vh;
        width: 280px;
        transform: translateX(-100%);
        z-index: 300;
        box-shadow: var(--shadow-xl);
    }

    .sidebar.open {
        transform: translateX(0);
    }

    .sidebar-toggle {
        display: flex;
    }

    .mobile-sidebar-toggle {
        display: flex;
    }

    .main-content {
        width: 100%;
    }

    .top-header {
        padding: var(--space-md);
        min-height: 60px;
    }

    .header-left {
        gap: var(--space-sm);
    }

    .chat-title {
        gap: var(--space-sm);
    }

    .chat-info h3 {
        font-size: 1rem;
        line-height: 1.3;
    }

    .status {
        font-size: 0.8rem;
    }

    .ai-avatar {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .disclaimer {
        padding: var(--space-sm) var(--space-md);
        font-size: 0.8rem;
        flex-wrap: wrap;
    }

    .chat-messages {
        padding: var(--space-md);
        gap: var(--space-md);
    }

    .welcome-message {
        padding: var(--space-md);
        gap: var(--space-sm);
    }

    .welcome-message .ai-avatar {
        width: 36px;
        height: 36px;
        font-size: 1rem;
    }

    .message {
        gap: var(--space-xs);
        margin-bottom: var(--space-md);
    }

    .user-avatar,
    .ai-avatar {
        width: 28px;
        height: 28px;
        font-size: 0.75rem;
    }

    .message-bubble {
        max-width: 85%;
        font-size: 0.9rem;
        padding: var(--space-sm) var(--space-md);
    }

    .input-area {
        padding: var(--space-md);
    }

    .input-wrapper {
        padding: 4px;
    }

    #user-input {
        font-size: 16px; /* Prevents zoom on iOS */
        padding: var(--space-sm) var(--space-md);
    }

    #send-btn {
        width: 40px;
        height: 40px;
        font-size: 0.9rem;
    }

    .powered-by {
        font-size: 0.75rem;
    }

    /* Touch-friendly hover states */
    .message-bubble:hover {
        transform: none;
    }

    .action-btn:hover {
        transform: none;
    }

    #send-btn:hover:not(:disabled) {
        transform: none;
    }
}

/* Small Mobile */
@media (max-width: 480px) {
    .chat-header {
        padding: var(--space-sm);
    }

    .chat-info h3 {
        font-size: 0.9rem;
    }

    .status {
        font-size: 0.75rem;
    }

    .disclaimer {
        padding: var(--space-xs) var(--space-sm);
        font-size: 0.75rem;
    }

    .chat-messages {
        padding: var(--space-sm);
    }

    .welcome-message {
        padding: var(--space-sm);
    }

    .message-bubble {
        max-width: 90%;
        font-size: 0.85rem;
        padding: var(--space-xs) var(--space-sm);
    }

    .input-area {
        padding: var(--space-sm);
    }

    #send-btn {
        width: 36px;
        height: 36px;
        font-size: 0.8rem;
    }
}

/* ===== ACCESSIBILITY ===== */

/* Focus Management */
.action-btn:focus,
#send-btn:focus,
#user-input:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    :root {
        --ai-message-border: #000000;
        --input-border: #000000;
        --text-muted: #666666;
    }

    .message-bubble {
        border: 2px solid currentColor;
    }

    .input-wrapper {
        border-width: 3px;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .typing-dot {
        animation: none;
    }

    .status::before {
        animation: none;
    }
}

/* Screen Reader Support */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Keyboard Navigation */
.chat-messages:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: -2px;
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */

/* GPU Acceleration for Smooth Animations */
.message,
.message-bubble,
.ai-avatar,
.user-avatar,
#send-btn,
.action-btn {
    will-change: transform;
    transform: translateZ(0);
}

/* Optimize Repaints */
.chat-messages {
    contain: layout style paint;
}

.message-bubble {
    contain: layout style;
}

/* ===== UTILITY CLASSES ===== */

/* Loading States */
.loading {
    pointer-events: none;
    opacity: 0.6;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent
    );
    animation: shimmer 1.5s infinite;
}

/* Error States */
.error {
    border-color: #ef4444 !important;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
}

.error-message {
    color: #dc2626;
    background: #fef2f2;
    border-left-color: #ef4444;
}

/* Success States */
.success {
    border-color: #10b981 !important;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1) !important;
}

/* ===== PRINT STYLES ===== */
@media print {
    .chat-layout {
        height: auto;
    }

    .chat-container {
        box-shadow: none;
        border: 1px solid #000;
    }

    .chat-header,
    .input-area {
        display: none;
    }

    .disclaimer {
        background: none;
        border: 1px solid #000;
        color: #000;
    }

    .message-bubble {
        box-shadow: none;
        border: 1px solid #000;
        break-inside: avoid;
    }

    .user-message .message-bubble {
        background: #f0f0f0 !important;
        color: #000 !important;
    }

    .ai-message .message-bubble {
        background: #fff !important;
        color: #000 !important;
    }
}

/* ===== AI AVATAR STYLES ===== */
.ai-avatar {
    width: 48px;
    height: 48px;
    background: var(--primary-gradient);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-inverse);
    font-size: 1.25rem;
    box-shadow: var(--shadow-md);
    transition: transform var(--transition-fast);
}

.ai-avatar:hover {
    transform: scale(1.05);
}

